"""
Beta Detection System - Configuration Manager
Handles loading and managing configuration for beta detection system.
"""

import yaml
import os
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class BetaConfigManager:
    """
    Manages configuration for beta detection system.
    """
    
    def __init__(self, config_path: str = None):
        """
        Initialize configuration manager.

        Args:
            config_path: Path to configuration file
        """
        if config_path is None:
            # Try multiple possible paths for the config file
            possible_paths = [
                "beta_config.yaml",  # When running from Beta_detection directory
                "Beta_detection/beta_config.yaml",  # When running from parent directory
                os.path.join(os.path.dirname(__file__), "beta_config.yaml")  # Relative to this file
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break
            else:
                # Default to the first option if none found
                config_path = "beta_config.yaml"

        self.config_path = config_path
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"Configuration loaded from {self.config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {self.config_path} not found, using defaults")
            return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}, using defaults")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if file is not available."""
        return {
            'beta_calculation': {
                'measurement_length': 100,
                'benchmark_symbol': 'BTC/USDT',
                'min_data_threshold': 0.8,
                'roc_threshold': 0.05
            },
            'assets': {
                'default_symbols': [
                    'SOL/USDT', 'ETH/USDT', 'DOGE/USDT', 'SHIB/USDT', 'PEPE/USDT',
                    'LINK/USDT', 'TRX/USDT', 'AAVE/USDT', 'XMR/USDT', 'BONK/USDT',
                    'XLM/USDT', 'STX/USDT', 'ATOM/USDT', 'CRO/USDT', 'MANA/USDT'
                ]
            },
            'data_fetching': {
                'timeframe': '1d',
                'use_cache': True,
                'force_refresh': False,
                'use_pagination': True,
                'max_pages': 5,
                'buffer_days': 30,
                'context': 'beta_analysis'
            },
            'display': {
                'max_assets_per_column': 15,
                'show_statistics': True,
                'show_insights': True,
                'save_results': True
            }
        }
    
    def get_measurement_length(self) -> int:
        """Get beta measurement length."""
        return self.config.get('beta_calculation', {}).get('measurement_length', 100)
    
    def get_benchmark_symbol(self) -> str:
        """Get benchmark symbol."""
        return self.config.get('beta_calculation', {}).get('benchmark_symbol', 'BTC/USDT')
    
    def get_roc_threshold(self) -> float:
        """Get ROC threshold for significant changes."""
        return self.config.get('beta_calculation', {}).get('roc_threshold', 0.05)
    
    def get_min_data_threshold(self) -> float:
        """Get minimum data threshold."""
        return self.config.get('beta_calculation', {}).get('min_data_threshold', 0.8)
    
    def get_default_symbols(self) -> List[str]:
        """Get default symbol list (all Binance assets)."""
        return self.config.get('assets', {}).get('default_symbols', [])
    
    def get_symbol_group(self, group_name: str) -> List[str]:
        """
        Get symbols for a specific group.
        
        Args:
            group_name: Name of the symbol group (e.g., 'defi_focus', 'meme_focus')
            
        Returns:
            List of symbols for the group
        """
        return self.config.get('assets', {}).get(group_name, [])
    
    def get_data_fetching_config(self) -> Dict[str, Any]:
        """Get data fetching configuration."""
        return self.config.get('data_fetching', {})
    
    def get_display_config(self) -> Dict[str, Any]:
        """Get display configuration."""
        return self.config.get('display', {})
    
    def get_analysis_mode_config(self, mode: str = 'standard') -> Dict[str, Any]:
        """
        Get configuration for specific analysis mode.
        
        Args:
            mode: Analysis mode ('quick', 'standard', 'comprehensive')
            
        Returns:
            Configuration for the specified mode
        """
        modes = self.config.get('analysis_modes', {})
        if mode in modes:
            return modes[mode]
        else:
            logger.warning(f"Analysis mode '{mode}' not found, using standard")
            return modes.get('standard', {})
    
    def get_since_date(self, mode: str = 'standard') -> str:
        """
        Calculate appropriate since date based on measurement length and buffer.
        
        Args:
            mode: Analysis mode to use for calculation
            
        Returns:
            Since date in YYYY-MM-DD format
        """
        mode_config = self.get_analysis_mode_config(mode)
        measurement_length = mode_config.get('measurement_length', self.get_measurement_length())
        buffer_days = mode_config.get('buffer_days', 
                                    self.config.get('data_fetching', {}).get('buffer_days', 30))
        
        total_days = measurement_length + buffer_days
        since_date = datetime.now() - timedelta(days=total_days)
        return since_date.strftime('%Y-%m-%d')
    
    def get_color_thresholds(self) -> Dict[str, float]:
        """Get color coding thresholds."""
        color_config = self.config.get('display', {}).get('color_coding', {})
        return {
            'high_beta': color_config.get('high_beta_threshold', 1.5),
            'medium_beta': color_config.get('medium_beta_threshold', 1.0)
        }
    
    def get_alert_config(self) -> Dict[str, Any]:
        """Get alert configuration."""
        return self.config.get('alerts', {})
    
    def get_validation_config(self) -> Dict[str, Any]:
        """Get validation configuration."""
        return self.config.get('validation', {})
    
    def should_save_results(self) -> bool:
        """Check if results should be saved."""
        return self.config.get('display', {}).get('save_results', True)
    
    def should_show_statistics(self) -> bool:
        """Check if statistics should be shown."""
        return self.config.get('display', {}).get('show_statistics', True)
    
    def should_show_insights(self) -> bool:
        """Check if insights should be shown."""
        return self.config.get('display', {}).get('show_insights', True)
    
    def get_max_assets_per_column(self) -> int:
        """Get maximum assets per column for display."""
        return self.config.get('display', {}).get('max_assets_per_column', 15)
    
    def get_results_directory(self) -> str:
        """Get results directory path."""
        return self.config.get('files', {}).get('results_directory', 'Beta_detection')
    
    def get_results_filename_pattern(self) -> str:
        """Get results filename pattern."""
        return self.config.get('files', {}).get('results_filename_pattern', 
                                               'beta_results_{timestamp}.json')
    
    def update_config(self, section: str, key: str, value: Any) -> None:
        """
        Update configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            value: New value
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
        logger.info(f"Updated config: {section}.{key} = {value}")
    
    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    def validate_config(self) -> List[str]:
        """
        Validate configuration and return list of issues.
        
        Returns:
            List of validation issues (empty if valid)
        """
        issues = []
        
        # Check required sections
        required_sections = ['beta_calculation', 'assets', 'data_fetching', 'display']
        for section in required_sections:
            if section not in self.config:
                issues.append(f"Missing required section: {section}")
        
        # Check measurement length
        measurement_length = self.get_measurement_length()
        if measurement_length < 20:
            issues.append(f"Measurement length too small: {measurement_length} (minimum: 20)")
        
        # Check benchmark symbol
        benchmark = self.get_benchmark_symbol()
        if not benchmark or not benchmark.endswith('/USDT'):
            issues.append(f"Invalid benchmark symbol: {benchmark}")
        
        # Check default symbols
        default_symbols = self.get_default_symbols()
        if not default_symbols:
            issues.append("No default symbols configured")
        elif len(default_symbols) > 40:
            issues.append(f"Too many default symbols: {len(default_symbols)} (maximum: 40)")
        
        return issues
    
    def print_config_summary(self) -> None:
        """Print a summary of current configuration."""
        print("\n" + "="*60)
        print("BETA DETECTION CONFIGURATION SUMMARY")
        print("="*60)
        
        print(f"Measurement Length: {self.get_measurement_length()} days")
        print(f"Benchmark Symbol: {self.get_benchmark_symbol()}")
        print(f"ROC Threshold: {self.get_roc_threshold()}")
        print(f"Default Symbols: {len(self.get_default_symbols())} assets")
        print(f"Max Assets/Column: {self.get_max_assets_per_column()}")
        print(f"Save Results: {self.should_save_results()}")
        print(f"Show Statistics: {self.should_show_statistics()}")
        print(f"Show Insights: {self.should_show_insights()}")
        
        # Show available symbol groups
        asset_groups = []
        for key in self.config.get('assets', {}):
            if key != 'default_symbols' and isinstance(self.config['assets'][key], list):
                asset_groups.append(f"{key} ({len(self.config['assets'][key])} assets)")

        if asset_groups:
            print(f"Available Groups:")
            for group in asset_groups:
                print(f"  - {group}")

        print("="*60)
